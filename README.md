# Cloudflare IP 优选脚本

基于原始 JavaScript `bestIP` 功能改写的 Python 脚本，用于获取特定国家的 Cloudflare IP 节点并进行优选测试。

## 功能特点

- 🌍 **多国家支持**: 可指定目标国家代码获取对应地区的IP
- 📊 **多IP源支持**: 支持CF官方、CM整理、AS列表等多种IP源
- ⚡ **高并发测试**: 支持自定义并发数，快速完成IP测试
- 🎯 **延迟优选**: 自动按延迟排序，筛选出最优IP节点
- 💾 **自动保存**: 测试结果自动保存为txt文件，支持覆盖更新
- 🤖 **GitHub Actions**: 支持定时自动运行和手动触发

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 基本用法

```bash
# 获取中国地区的IP（默认参数）
python ip_optimizer.py

# 获取美国地区的IP
python ip_optimizer.py --country US

# 使用CM整理的IP源
python ip_optimizer.py --source cm

# 指定端口和并发数
python ip_optimizer.py --port 2053 --concurrent 64
```

### 完整参数说明

```bash
python ip_optimizer.py [选项]

选项:
  --country, -c     目标国家代码 (默认: CN)
  --source, -s      IP源选择 (默认: official)
                    可选值: official, cm, as13335, as209242, as24429, as35916, as199524, proxyip
  --port, -p        目标端口 (默认: 443)
  --max-ips, -m     最大IP数量 (默认: 512)
  --concurrent      并发数 (默认: 32)
  --output, -o      输出文件名 (默认: nodes.txt)
```

### IP源说明

| 源名称 | 说明 |
|--------|------|
| `official` | Cloudflare官方IP列表 |
| `cm` | CM整理的IP列表 |
| `as13335` | AS13335 Cloudflare全段IP |
| `as209242` | AS209242 Cloudflare非官方IP |
| `as24429` | AS24429 Alibaba IP段 |
| `as35916` | AS35916 IP段 |
| `as199524` | AS199524 G-Core IP段 |
| `proxyip` | 反代IP列表 |

## GitHub Actions 自动化

本项目支持通过 GitHub Actions 自动运行IP优选：

### 定时运行
- 每天北京时间早上8点自动运行
- 使用默认参数（CN国家，official源，443端口）

### 手动触发
1. 进入 GitHub 仓库的 Actions 页面
2. 选择 "Update Cloudflare IPs" 工作流
3. 点击 "Run workflow"
4. 可自定义参数：
   - 目标国家代码
   - IP源
   - 端口
   - 最大IP数量

### 输出文件
- 优选结果会自动提交到仓库的 `nodes.txt` 文件
- 手动触发时会创建 Release 并上传文件
- 工作流会生成详细的运行摘要

## 输出格式

生成的 `nodes.txt` 文件格式如下：

```
**********:443#US 官方优选 45ms
**********:443#US 官方优选 52ms
**********:443#US 官方优选 58ms
```

每行包含：
- IP地址和端口
- 国家代码
- IP类型（官方优选/反代优选）
- 延迟时间

## 注意事项

1. **网络环境**: 建议在直连网络环境下运行，避免代理影响测试结果
2. **并发限制**: 过高的并发数可能导致网络拥塞，建议根据网络环境调整
3. **IP数量**: 默认最多测试512个IP，可根据需要调整
4. **超时设置**: 单个IP测试超时时间为5秒，失败会自动重试3次

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！

## 更新日志

### v1.0.0
- 初始版本发布
- 支持多国家IP获取
- 支持多种IP源
- 集成GitHub Actions自动化
