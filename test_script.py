#!/usr/bin/env python3
"""
测试脚本 - 验证IP优选功能
"""

import asyncio
import sys
from ip_optimizer import CloudflareIPOptimizer

async def test_basic_functionality():
    """测试基本功能"""
    print("🧪 开始基本功能测试...")
    
    try:
        async with CloudflareIPOptimizer(
            target_country="US",  # 测试美国IP
            max_ips=50,  # 限制IP数量以加快测试
            max_concurrent=16  # 降低并发数
        ) as optimizer:
            
            # 测试IP获取
            print("\n1️⃣ 测试IP获取功能...")
            ips = await optimizer.get_cf_ips("official", "443")
            print(f"获取到 {len(ips)} 个IP")
            
            if ips:
                print("前5个IP:")
                for i, ip in enumerate(ips[:5], 1):
                    print(f"  {i}. {ip}")
            
            # 测试单个IP
            print("\n2️⃣ 测试单个IP测试功能...")
            if ips:
                test_ip = ips[0].split(':')[0] if ':' in ips[0] else ips[0]
                result = await optimizer.test_ip(test_ip, 443)
                if result:
                    print(f"测试成功: {result.to_display_format()}")
                else:
                    print("测试失败")
            
            # 测试完整流程（限制数量）
            print("\n3️⃣ 测试完整优选流程...")
            results = await optimizer.get_country_ips("official", "443")
            
            if results:
                print(f"✅ 找到 {len(results)} 个美国IP")
                print("前3个最优IP:")
                for i, result in enumerate(results[:3], 1):
                    print(f"  {i}. {result.to_display_format()}")
                
                # 测试保存功能
                print("\n4️⃣ 测试文件保存功能...")
                optimizer.save_results_to_file(results, "test_nodes.txt")
                
            else:
                print("❌ 未找到任何美国IP")
                
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False
    
    print("\n✅ 基本功能测试完成")
    return True

async def test_different_sources():
    """测试不同IP源"""
    print("\n🧪 测试不同IP源...")
    
    sources = ["official", "cm"]  # 只测试主要的两个源
    
    for source in sources:
        print(f"\n📊 测试 {source} 源...")
        try:
            async with CloudflareIPOptimizer(
                target_country="CN",
                max_ips=20,  # 进一步限制数量
                max_concurrent=8
            ) as optimizer:
                
                ips = await optimizer.get_cf_ips(source, "443")
                print(f"  {source} 源获取到 {len(ips)} 个IP")
                
        except Exception as e:
            print(f"  ❌ {source} 源测试失败: {e}")

async def main():
    """主测试函数"""
    print("🚀 开始IP优选脚本测试")
    print("=" * 60)
    
    # 基本功能测试
    success = await test_basic_functionality()
    
    if success:
        # 不同源测试
        await test_different_sources()
    
    print("\n" + "=" * 60)
    print("🏁 测试完成")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        sys.exit(1)
