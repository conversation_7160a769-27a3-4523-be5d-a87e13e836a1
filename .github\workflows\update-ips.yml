name: Update Cloudflare IPs

# 添加权限设置
permissions:
  contents: write
  actions: read
  issues: write
  pull-requests: write

on:
  # 定时触发：每天北京时间早上8点（UTC 0点）
  schedule:
    - cron: '0 0 * * *'

  # 手动触发
  workflow_dispatch:
    inputs:
      country:
        description: '目标国家代码'
        required: false
        default: 'JP,SG'
        type: string
      count:
        description: '目标IP数量'
        required: false
        default: '10'
        type: string
      port:
        description: '目标端口'
        required: false
        default: '443'
        type: string
      max_ips:
        description: '每个库最大IP数量'
        required: false
        default: '512'
        type: string

jobs:
  update-ips:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install aiohttp
        pip install requests  # 备用HTTP库

    - name: Test network connectivity
      run: |
        echo "🌐 测试网络连接..."
        echo "Testing DNS resolution..."
        nslookup ******* || echo "DNS resolution failed"
        echo "Testing HTTP connectivity..."
        curl -I --connect-timeout 5 https://*******/dns-query || echo "Cloudflare DNS API 连接失败"
        curl -I --connect-timeout 5 https://www.cloudflare.com || echo "Cloudflare 网站连接失败"
        
    - name: Set parameters
      id: params
      run: |
        # 设置默认参数
        COUNTRY="${{ github.event.inputs.country || 'CN' }}"
        COUNT="${{ github.event.inputs.count || '10' }}"
        PORT="${{ github.event.inputs.port || '443' }}"
        MAX_IPS="${{ github.event.inputs.max_ips || '512' }}"

        echo "country=$COUNTRY" >> $GITHUB_OUTPUT
        echo "count=$COUNT" >> $GITHUB_OUTPUT
        echo "port=$PORT" >> $GITHUB_OUTPUT
        echo "max_ips=$MAX_IPS" >> $GITHUB_OUTPUT

        echo "Parameters set:"
        echo "Country: $COUNTRY"
        echo "Count: $COUNT"
        echo "Port: $PORT"
        echo "Max IPs per source: $MAX_IPS"
        
    - name: Run IP optimizer
      run: |
        echo "🚀 开始运行IP优选脚本..."
        echo "参数: 国家=${{ steps.params.outputs.country }}, 数量=${{ steps.params.outputs.count }}, 端口=${{ steps.params.outputs.port }}"

        python ip_optimizer.py \
          --country "${{ steps.params.outputs.country }}" \
          --count "${{ steps.params.outputs.count }}" \
          --port "${{ steps.params.outputs.port }}" \
          --max-ips 50 \
          --concurrent 8 \
          --output "nodes.txt"
      env:
        PYTHONUNBUFFERED: 1
        PYTHONIOENCODING: utf-8
        GITHUB_ACTIONS: true
          
    - name: Check if nodes.txt was created
      run: |
        if [ -f "nodes.txt" ]; then
          echo "nodes.txt created successfully"
          echo "File size: $(wc -l < nodes.txt) lines"
          echo "First 5 lines:"
          head -5 nodes.txt
        else
          echo "nodes.txt was not created"
          exit 1
        fi
        
    - name: Commit and push changes
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        
        # 检查是否有变化
        if git diff --quiet nodes.txt; then
          echo "No changes to commit"
        else
          git add nodes.txt
          git commit -m "Auto update nodes.txt - ${{ steps.params.outputs.count }} ${{ steps.params.outputs.country }} IPs ($(date '+%Y-%m-%d %H:%M:%S'))"
          git push
          echo "Changes committed and pushed"
        fi
        
    - name: Create release (optional)
      if: github.event_name == 'workflow_dispatch'
      uses: softprops/action-gh-release@v1
      with:
        tag_name: nodes-${{ steps.params.outputs.country }}-${{ github.run_number }}
        name: ${{ steps.params.outputs.country }} Nodes - ${{ github.run_number }}
        body: |
          自动更新的 ${{ steps.params.outputs.country }} 国家 Cloudflare IP 节点列表

          **参数信息:**
          - 国家: ${{ steps.params.outputs.country }}
          - 目标数量: ${{ steps.params.outputs.count }}
          - 端口: ${{ steps.params.outputs.port }}
          - 每库最大IP数: 50
          - 更新时间: ${{ github.run_id }}

          **文件说明:**
          - `nodes.txt`: 包含优选的IP节点列表，按延迟排序
          - 脚本遍历所有IP库直到找到指定数量的目标国家IP
        files: nodes.txt
        draft: false
        prerelease: false
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Upload nodes.txt as artifact
      uses: actions/upload-artifact@v4
      with:
        name: nodes-${{ steps.params.outputs.country }}-${{ steps.params.outputs.count }}
        path: nodes.txt
        retention-days: 30
        
    - name: Summary
      run: |
        echo "## 🎉 IP优选完成" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### 📊 运行参数" >> $GITHUB_STEP_SUMMARY
        echo "- **目标国家**: ${{ steps.params.outputs.country }}" >> $GITHUB_STEP_SUMMARY
        echo "- **目标数量**: ${{ steps.params.outputs.count }}" >> $GITHUB_STEP_SUMMARY
        echo "- **端口**: ${{ steps.params.outputs.port }}" >> $GITHUB_STEP_SUMMARY
        echo "- **每库最大IP数**: ${{ steps.params.outputs.max_ips }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        
        if [ -f "nodes.txt" ]; then
          NODE_COUNT=$(wc -l < nodes.txt)
          echo "### 📋 结果统计" >> $GITHUB_STEP_SUMMARY
          echo "- **获取节点数**: $NODE_COUNT" >> $GITHUB_STEP_SUMMARY
          echo "- **文件大小**: $(du -h nodes.txt | cut -f1)" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          echo "### 🔝 前5个最优节点" >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
          head -5 nodes.txt >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
        else
          echo "### ❌ 错误" >> $GITHUB_STEP_SUMMARY
          echo "未能生成节点文件" >> $GITHUB_STEP_SUMMARY
        fi
